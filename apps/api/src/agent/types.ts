export interface Identifiable {
  id: string;
  name: string;
}

export interface Category extends Identifiable {
  description?: string;
  icon_url?: string;
  priority?: number; // Lower numbers = higher priority (e.g., 1 = first, 2 = second, etc.)
}

export interface Tag extends Identifiable {}

export interface ItemData {
  name: string;
  description: string;
  featured?: boolean;
  source_url: string;
  category: string | string[] | Category | Category[];
  slug?: string;
  tags: string[] | Tag[];
}
